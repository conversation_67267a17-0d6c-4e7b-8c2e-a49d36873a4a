import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import apiService from '../../services/apiService';
import AssessmentForm from './AssessmentForm';
import LoadingSpinner from '../UI/LoadingSpinner';
import { useAuth } from '../../context/AuthContext';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchParams] = useSearchParams();
  const isDebugMode = searchParams.get('debug') === 'true';

  const [currentStep, setCurrentStep] = useState(1);
  const [assessmentResults, setAssessmentResults] = useState({
    via: null,
    riasec: null,
    bigFive: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tokenBalance, setTokenBalance] = useState(null);
  const [isCheckingToken, setIsCheckingToken] = useState(false);

  const assessments = [
    { key: 'via', data: viaQuestions, title: 'VIA Character Strengths' },
    { key: 'riasec', data: riasecQuestions, title: 'RIASEC Holland Codes' },
    { key: 'bigFive', data: bigFiveQuestions, title: 'Big Five Personality' }
  ];

  // Debug function to auto-fill assessments
  const generateDebugScores = () => {
    const debugScores = {
      via: {
        creativity: 85,
        curiosity: 78,
        judgment: 70,
        loveOfLearning: 82,
        perspective: 60,
        bravery: 55,
        perseverance: 68,
        honesty: 73,
        zest: 66,
        love: 80,
        kindness: 75,
        socialIntelligence: 65,
        teamwork: 60,
        fairness: 70,
        leadership: 67,
        forgiveness: 58,
        humility: 62,
        prudence: 69,
        selfRegulation: 61,
        appreciationOfBeauty: 50,
        gratitude: 72,
        hope: 77,
        humor: 65,
        spirituality: 55
      },
      riasec: {
        realistic: 75,
        investigative: 85,
        artistic: 60,
        social: 50,
        enterprising: 70,
        conventional: 55
      },
      bigFive: {
        openness: 80,
        conscientiousness: 65,
        extraversion: 55,
        agreeableness: 45,
        neuroticism: 30
      }
    };
    return debugScores;
  };

  const currentAssessment = assessments[currentStep - 1];

  // Auto-fill assessments in debug mode
  useEffect(() => {
    if (isDebugMode && import.meta.env.DEV) {
      console.log('🔧 DEBUG MODE: Auto-filling assessments...');
      const debugScores = generateDebugScores();
      setAssessmentResults(debugScores);
      setCurrentStep(3); // Go to last assessment
    }
  }, [isDebugMode]);

  // Fetch token balance on component mount
  useEffect(() => {
    const fetchTokenBalance = async () => {
      try {
        const response = await apiService.getTokenBalance();
        if (response.success) {
          setTokenBalance(response.data.token_balance);
        }
      } catch (err) {
        console.error('Failed to fetch token balance:', err);
        setTokenBalance(0); // Set to 0 if failed to fetch
      }
    };

    fetchTokenBalance();
  }, []);

  // Function to check token balance before submission
  const checkTokenBalance = async () => {
    setIsCheckingToken(true);
    try {
      const response = await apiService.getTokenBalance();
      if (response.success) {
        const balance = response.data.token_balance;
        setTokenBalance(balance);
        return balance;
      }
      return 0;
    } catch (err) {
      console.error('Failed to check token balance:', err);
      return 0;
    } finally {
      setIsCheckingToken(false);
    }
  };

  const handleAssessmentComplete = (scores) => {
    console.log('=== ASSESSMENT COMPLETE ===');
    console.log('Current assessment key:', currentAssessment.key);
    console.log('Received scores:', scores);
    console.log('Previous assessment results:', assessmentResults);

    const newResults = {
      ...assessmentResults,
      [currentAssessment.key]: scores
    };

    console.log('New assessment results:', newResults);
    setAssessmentResults(newResults);

    // Only move to next assessment if not the last one
    if (currentStep < assessments.length) {
      console.log('Moving to next step:', currentStep + 1);
      setCurrentStep(prev => prev + 1);
    } else {
      console.log('This was the last assessment - ready for final submission');
      // Don't auto-advance, let user explicitly click submit
    }
  };

  const handleNext = () => {
    if (currentStep < assessments.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const transformScoresForAPI = () => {
    const { via, riasec, bigFive } = assessmentResults;

    // Transform VIA scores to match API format with all required fields
    const viaIs = {
      creativity: (via?.creativity || 0),
      curiosity: (via?.curiosity || 0),
      judgment: (via?.judgment || 0),
      loveOfLearning: (via?.loveOfLearning || 0),
      perspective: (via?.perspective || 0),
      bravery: (via?.bravery || 0),
      perseverance: (via?.perseverance || 0),
      honesty: (via?.honesty || 0),
      zest: (via?.zest || 0),
      love: (via?.love || 0),
      kindness: (via?.kindness || 0),
      socialIntelligence: (via?.socialIntelligence || 0),
      teamwork: (via?.teamwork || 0),
      fairness: (via?.fairness || 0),
      leadership: (via?.leadership || 0),
      forgiveness: (via?.forgiveness || 0),
      humility: (via?.humility || 0),
      prudence: (via?.prudence || 0),
      selfRegulation: (via?.selfRegulation || 0),
      appreciationOfBeauty: (via?.appreciationOfBeauty || 0),
      gratitude: (via?.gratitude || 0),
      hope: (via?.hope || 0),
      humor: (via?.humor || 0),
      spirituality: (via?.spirituality || 0)
    };

    // Transform RIASEC scores to match API format
    const riasecScores = {
      realistic: (riasec?.realistic || 0),
      investigative: (riasec?.investigative || 0),
      artistic: (riasec?.artistic || 0),
      social: (riasec?.social || 0),
      enterprising: (riasec?.enterprising || 0),
      conventional: (riasec?.conventional || 0)
    };

    // Transform Big Five scores to match API format (OCEAN)
    const ocean = {
      openness: (bigFive?.openness || 0),
      conscientiousness: (bigFive?.conscientiousness || 0),
      extraversion: (bigFive?.extraversion || 0),
      agreeableness: (bigFive?.agreeableness || 0),
      neuroticism: (bigFive?.neuroticism || 0)
    };

    const transformedData = {
      riasec: riasecScores,
      ocean: ocean,
      viaIs: viaIs
    };

    // Log the transformed data for debugging
    console.log('Assessment Results:', assessmentResults);
    console.log('Transformed Data for API:', transformedData);

    return transformedData;
  };

  const findIncompleteAssessment = () => {
    const { via, riasec, bigFive } = assessmentResults;

    console.log('=== ASSESSMENT VALIDATION ===');
    console.log('VIA results:', via);
    console.log('RIASEC results:', riasec);
    console.log('Big Five results:', bigFive);

    // Check if each assessment has meaningful data (not just empty object)
    const isViaComplete = via && Object.keys(via).length > 0 && Object.values(via).some(score => score > 0);
    const isRiasecComplete = riasec && Object.keys(riasec).length > 0 && Object.values(riasec).some(score => score > 0);
    const isBigFiveComplete = bigFive && Object.keys(bigFive).length > 0 && Object.values(bigFive).some(score => score > 0);

    console.log('VIA complete:', isViaComplete);
    console.log('RIASEC complete:', isRiasecComplete);
    console.log('Big Five complete:', isBigFiveComplete);

    if (!isViaComplete) return { step: 1, name: 'VIA Character Strengths' };
    if (!isRiasecComplete) return { step: 2, name: 'RIASEC Holland Codes' };
    if (!isBigFiveComplete) return { step: 3, name: 'Big Five Personality' };
    return null;
  };

  const handleSubmitAll = async () => {
    // Enhanced debugging
    console.log('=== SUBMIT DEBUG ===');
    console.log('Current assessmentResults:', assessmentResults);
    console.log('Current step:', currentStep);
    console.log('Total assessments:', assessments.length);
    console.log('isSubmitting:', isSubmitting);

    // Prevent double submission
    if (isSubmitting) {
      console.log('⚠️ Already submitting, ignoring duplicate call');
      return;
    }

    // Validate that all assessments are completed
    const incompleteAssessment = findIncompleteAssessment();
    console.log('Incomplete assessment check:', incompleteAssessment);

    if (incompleteAssessment) {
      console.log('Found incomplete assessment:', incompleteAssessment);
      setError(`Please complete the ${incompleteAssessment.name} assessment before submitting. Redirecting you to that assessment...`);

      // Redirect to incomplete assessment after a short delay
      setTimeout(() => {
        setCurrentStep(incompleteAssessment.step);
        setError('');
      }, 2000);
      return;
    }

    console.log('All assessments complete, proceeding with submission...');
    setIsSubmitting(true);
    setError('');

    try {
      // Check token balance before submitting
      const currentBalance = await checkTokenBalance();

      if (currentBalance <= 0) {
        setError('Insufficient token balance. You need at least 1 token to submit an assessment. Please contact support to add more tokens to your account.');
        setIsSubmitting(false);
        return;
      }

      const transformedData = transformScoresForAPI();
      console.log('Submitting data to API:', transformedData);

      const response = await apiService.submitAssessment(transformedData);
      console.log('API Response:', response);

      if (response.success) {
        const { jobId } = response.data;
        console.log('Navigating to status page with jobId:', jobId);
        // Navigate to status page with jobId and state indicating we came from submission
        navigate(`/assessment/status/${jobId}`, {
          state: { fromSubmission: true }
        });
      } else {
        console.error('API returned success: false', response);
        setError('Failed to submit assessment: ' + (response.message || 'Unknown error'));
      }
    } catch (err) {
      console.error('Submit error:', err);
      setError(err.response?.data?.message || err.message || 'Failed to submit assessment');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner
          size="xl"
          text="Submitting Assessment... Transforming your data for AI analysis."
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {error && (
        <div className="max-w-3xl mx-auto px-4 py-4">
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Assessment Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Debug Mode Indicator */}
      {isDebugMode && import.meta.env.DEV && (
        <div className="max-w-3xl mx-auto px-4 mb-4">
          <div className="bg-orange-100 border border-orange-300 rounded-lg p-3 flex items-center space-x-2">
            <span className="text-orange-600">🔧</span>
            <span className="text-orange-800 font-medium text-sm">
              DEBUG MODE: Assessments are auto-filled for testing
            </span>
          </div>
        </div>
      )}

      <AssessmentForm
        key={currentStep} // Force re-render when step changes to reset state
        assessmentData={currentAssessment.data}
        onSubmit={currentStep === assessments.length ? handleSubmitAll : handleAssessmentComplete}
        onNext={handleNext}
        onPrevious={handlePrevious}
        isLastAssessment={currentStep === assessments.length}
        currentStep={currentStep}
        totalSteps={assessments.length}
        tokenBalance={tokenBalance}
        isCheckingToken={isCheckingToken}
        isDebugMode={isDebugMode}
      />
    </div>
  );
};

export default AssessmentFlow;
